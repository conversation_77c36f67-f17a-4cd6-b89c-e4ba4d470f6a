  Manifest android  CAMERA android.Manifest.permission  SuppressLint android.annotation  Activity android.app  Application android.app  ActivityMainBinding android.app.Activity  Boolean android.app.Activity  BroadCastUtils android.app.Activity  ExperimentalGetImage android.app.Activity  Int android.app.Activity  Intent android.app.Activity  RequestPermission android.app.Activity  ScanResultsHolder android.app.Activity  SeekBar android.app.Activity  String android.app.Activity  Toast android.app.Activity  View android.app.Activity  apply android.app.Activity  clearResults android.app.Activity  format android.app.Activity  getLatestResult android.app.Activity  getValue android.app.Activity  
isNotEmpty android.app.Activity  joinToString android.app.Activity  layoutInflater android.app.Activity  let android.app.Activity  onCreate android.app.Activity  provideDelegate android.app.Activity  takeLast android.app.Activity  	viewModel android.app.Activity  
viewModels android.app.Activity  
windowManager android.app.Activity  OnSeekBarChangeListener android.app.Activity.SeekBar  Context android.content  Intent android.content  ActivityMainBinding android.content.Context  Boolean android.content.Context  BroadCastUtils android.content.Context  ExperimentalGetImage android.content.Context  Int android.content.Context  Intent android.content.Context  RequestPermission android.content.Context  ScanResultsHolder android.content.Context  SeekBar android.content.Context  String android.content.Context  Toast android.content.Context  View android.content.Context  apply android.content.Context  clearResults android.content.Context  format android.content.Context  getLatestResult android.content.Context  getValue android.content.Context  
isNotEmpty android.content.Context  joinToString android.content.Context  let android.content.Context  provideDelegate android.content.Context  takeLast android.content.Context  	viewModel android.content.Context  
viewModels android.content.Context  OnSeekBarChangeListener android.content.Context.SeekBar  ActivityMainBinding android.content.ContextWrapper  Boolean android.content.ContextWrapper  BroadCastUtils android.content.ContextWrapper  ExperimentalGetImage android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  RequestPermission android.content.ContextWrapper  ScanResultsHolder android.content.ContextWrapper  SeekBar android.content.ContextWrapper  String android.content.ContextWrapper  Toast android.content.ContextWrapper  View android.content.ContextWrapper  apply android.content.ContextWrapper  clearResults android.content.ContextWrapper  format android.content.ContextWrapper  getLatestResult android.content.ContextWrapper  getValue android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  joinToString android.content.ContextWrapper  let android.content.ContextWrapper  provideDelegate android.content.ContextWrapper  
sendBroadcast android.content.ContextWrapper  takeLast android.content.ContextWrapper  	viewModel android.content.ContextWrapper  
viewModels android.content.ContextWrapper  OnSeekBarChangeListener &android.content.ContextWrapper.SeekBar  BroadCastUtils android.content.Intent  apply android.content.Intent  putExtra android.content.Intent  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  AttributeSet android.graphics  Barcode android.graphics  Bitmap android.graphics  Canvas android.graphics  Color android.graphics  Context android.graphics  DetectedQRInfo android.graphics  Float android.graphics  ImageFormat android.graphics  Int android.graphics  JvmOverloads android.graphics  List android.graphics  Log android.graphics  Paint android.graphics  Point android.graphics  Rect android.graphics  RectF android.graphics  String android.graphics  Typeface android.graphics  View android.graphics  apply android.graphics  forEach android.graphics  let android.graphics  minOf android.graphics  
mutableListOf android.graphics  height android.graphics.Bitmap  scale android.graphics.Bitmap  width android.graphics.Bitmap  drawLine android.graphics.Canvas  drawRect android.graphics.Canvas  
drawRoundRect android.graphics.Canvas  drawText android.graphics.Canvas  BLACK android.graphics.Color  GREEN android.graphics.Color  Color android.graphics.Paint  Paint android.graphics.Paint  Style android.graphics.Paint  Typeface android.graphics.Paint  alpha android.graphics.Paint  apply android.graphics.Paint  color android.graphics.Paint  
getTextBounds android.graphics.Paint  isAntiAlias android.graphics.Paint  strokeWidth android.graphics.Paint  style android.graphics.Paint  textSize android.graphics.Paint  typeface android.graphics.Paint  STROKE android.graphics.Paint.Style  x android.graphics.Point  y android.graphics.Point  bottom android.graphics.Rect  height android.graphics.Rect  left android.graphics.Rect  let android.graphics.Rect  right android.graphics.Rect  top android.graphics.Rect  width android.graphics.Rect  bottom android.graphics.RectF  height android.graphics.RectF  left android.graphics.RectF  right android.graphics.RectF  top android.graphics.RectF  width android.graphics.RectF  DEFAULT_BOLD android.graphics.Typeface  CameraCharacteristics android.hardware.camera2  Image 
android.media  height android.media.Image  width android.media.Image  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  R android.os.Build.VERSION_CODES  AttributeSet android.util  DisplayMetrics android.util  Log android.util  Range android.util  Rational android.util  Size android.util  heightPixels android.util.DisplayMetrics  widthPixels android.util.DisplayMetrics  d android.util.Log  e android.util.Log  w android.util.Log  lower android.util.Range  upper android.util.Range  toFloat android.util.Rational  height android.util.Size  width android.util.Size  LayoutInflater android.view  MotionEvent android.view  View android.view  
WindowMetrics android.view  ActivityMainBinding  android.view.ContextThemeWrapper  Boolean  android.view.ContextThemeWrapper  BroadCastUtils  android.view.ContextThemeWrapper  ExperimentalGetImage  android.view.ContextThemeWrapper  Int  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  RequestPermission  android.view.ContextThemeWrapper  ScanResultsHolder  android.view.ContextThemeWrapper  SeekBar  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  apply  android.view.ContextThemeWrapper  clearResults  android.view.ContextThemeWrapper  format  android.view.ContextThemeWrapper  getLatestResult  android.view.ContextThemeWrapper  getValue  android.view.ContextThemeWrapper  
isNotEmpty  android.view.ContextThemeWrapper  joinToString  android.view.ContextThemeWrapper  let  android.view.ContextThemeWrapper  provideDelegate  android.view.ContextThemeWrapper  takeLast  android.view.ContextThemeWrapper  	viewModel  android.view.ContextThemeWrapper  
viewModels  android.view.ContextThemeWrapper  OnSeekBarChangeListener (android.view.ContextThemeWrapper.SeekBar  
getMetrics android.view.Display  Color android.view.View  DetectedQRInfo android.view.View  GONE android.view.View  Log android.view.View  OnClickListener android.view.View  Paint android.view.View  Rect android.view.View  RectF android.view.View  Typeface android.view.View  VISIBLE android.view.View  apply android.view.View  height android.view.View  
invalidate android.view.View  	isEnabled android.view.View  let android.view.View  minOf android.view.View  
mutableListOf android.view.View  onDraw android.view.View  setOnClickListener android.view.View  
visibility android.view.View  width android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  currentWindowMetrics android.view.WindowManager  defaultDisplay android.view.WindowManager  bounds android.view.WindowMetrics  Button android.widget  LinearLayout android.widget  SeekBar android.widget  TextView android.widget  Toast android.widget  	isEnabled android.widget.Button  setOnClickListener android.widget.Button  text android.widget.Button  
visibility android.widget.Button  
visibility android.widget.LinearLayout  max android.widget.ProgressBar  progress android.widget.ProgressBar  OnSeekBarChangeListener android.widget.SeekBar  progress android.widget.SeekBar  setOnSeekBarChangeListener android.widget.SeekBar  text android.widget.TextView  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  
viewModels androidx.activity  ActivityMainBinding #androidx.activity.ComponentActivity  Boolean #androidx.activity.ComponentActivity  BroadCastUtils #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  Button #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  ExperimentalGetImage #androidx.activity.ComponentActivity  Float #androidx.activity.ComponentActivity  Int #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  OptIn #androidx.activity.ComponentActivity  QrScanViewModel #androidx.activity.ComponentActivity  RequestPermission #androidx.activity.ComponentActivity  ScanResultsHolder #androidx.activity.ComponentActivity  SeekBar #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  apply #androidx.activity.ComponentActivity  clearResults #androidx.activity.ComponentActivity  format #androidx.activity.ComponentActivity  getLatestResult #androidx.activity.ComponentActivity  getValue #androidx.activity.ComponentActivity  
isNotEmpty #androidx.activity.ComponentActivity  joinToString #androidx.activity.ComponentActivity  let #androidx.activity.ComponentActivity  provideDelegate #androidx.activity.ComponentActivity  registerForActivityResult #androidx.activity.ComponentActivity  takeLast #androidx.activity.ComponentActivity  	viewModel #androidx.activity.ComponentActivity  
viewModels #androidx.activity.ComponentActivity  ActivityMainBinding -androidx.activity.ComponentActivity.Companion  BroadCastUtils -androidx.activity.ComponentActivity.Companion  ExperimentalGetImage -androidx.activity.ComponentActivity.Companion  Intent -androidx.activity.ComponentActivity.Companion  RequestPermission -androidx.activity.ComponentActivity.Companion  ScanResultsHolder -androidx.activity.ComponentActivity.Companion  String -androidx.activity.ComponentActivity.Companion  Toast -androidx.activity.ComponentActivity.Companion  View -androidx.activity.ComponentActivity.Companion  apply -androidx.activity.ComponentActivity.Companion  clearResults -androidx.activity.ComponentActivity.Companion  format -androidx.activity.ComponentActivity.Companion  getLatestResult -androidx.activity.ComponentActivity.Companion  getValue -androidx.activity.ComponentActivity.Companion  
isNotEmpty -androidx.activity.ComponentActivity.Companion  joinToString -androidx.activity.ComponentActivity.Companion  let -androidx.activity.ComponentActivity.Companion  provideDelegate -androidx.activity.ComponentActivity.Companion  takeLast -androidx.activity.ComponentActivity.Companion  	viewModel -androidx.activity.ComponentActivity.Companion  
viewModels -androidx.activity.ComponentActivity.Companion  OnSeekBarChangeListener +androidx.activity.ComponentActivity.SeekBar  ActivityResultCallback androidx.activity.result  ActivityResultLauncher androidx.activity.result  <SAM-CONSTRUCTOR> /androidx.activity.result.ActivityResultCallback  launch /androidx.activity.result.ActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  RequestPermission 9androidx.activity.result.contract.ActivityResultContracts  OptIn androidx.annotation  AppCompatActivity androidx.appcompat.app  ActivityMainBinding (androidx.appcompat.app.AppCompatActivity  Boolean (androidx.appcompat.app.AppCompatActivity  BroadCastUtils (androidx.appcompat.app.AppCompatActivity  ExperimentalGetImage (androidx.appcompat.app.AppCompatActivity  Int (androidx.appcompat.app.AppCompatActivity  Intent (androidx.appcompat.app.AppCompatActivity  RequestPermission (androidx.appcompat.app.AppCompatActivity  ScanResultsHolder (androidx.appcompat.app.AppCompatActivity  SeekBar (androidx.appcompat.app.AppCompatActivity  String (androidx.appcompat.app.AppCompatActivity  Toast (androidx.appcompat.app.AppCompatActivity  View (androidx.appcompat.app.AppCompatActivity  apply (androidx.appcompat.app.AppCompatActivity  clearResults (androidx.appcompat.app.AppCompatActivity  format (androidx.appcompat.app.AppCompatActivity  getLatestResult (androidx.appcompat.app.AppCompatActivity  getValue (androidx.appcompat.app.AppCompatActivity  
isNotEmpty (androidx.appcompat.app.AppCompatActivity  joinToString (androidx.appcompat.app.AppCompatActivity  let (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  	onDestroy (androidx.appcompat.app.AppCompatActivity  provideDelegate (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  takeLast (androidx.appcompat.app.AppCompatActivity  	viewModel (androidx.appcompat.app.AppCompatActivity  
viewModels (androidx.appcompat.app.AppCompatActivity  OnSeekBarChangeListener 0androidx.appcompat.app.AppCompatActivity.SeekBar  Camera2CameraInfo androidx.camera.camera2.interop  ExperimentalCamera2Interop androidx.camera.camera2.interop  Camera androidx.camera.core  
CameraControl androidx.camera.core  
CameraInfo androidx.camera.core  CameraSelector androidx.camera.core  ConcurrentCamera androidx.camera.core  ExperimentalGetImage androidx.camera.core  
ExposureState androidx.camera.core  
ImageAnalysis androidx.camera.core  	ImageInfo androidx.camera.core  
ImageProxy androidx.camera.core  Preview androidx.camera.core  ResolutionInfo androidx.camera.core  UseCase androidx.camera.core  	ZoomState androidx.camera.core  
cameraControl androidx.camera.core.Camera  
cameraInfo androidx.camera.core.Camera  let androidx.camera.core.Camera  enableTorch "androidx.camera.core.CameraControl  setExposureCompensationIndex "androidx.camera.core.CameraControl  setZoomRatio "androidx.camera.core.CameraControl  
exposureState androidx.camera.core.CameraInfo  hasFlashUnit androidx.camera.core.CameraInfo  	zoomState androidx.camera.core.CameraInfo  DEFAULT_BACK_CAMERA #androidx.camera.core.CameraSelector  exposureCompensationIndex "androidx.camera.core.ExposureState  exposureCompensationRange "androidx.camera.core.ExposureState  exposureCompensationStep "androidx.camera.core.ExposureState  Analyzer "androidx.camera.core.ImageAnalysis  Builder "androidx.camera.core.ImageAnalysis  STRATEGY_KEEP_ONLY_LATEST "androidx.camera.core.ImageAnalysis  also "androidx.camera.core.ImageAnalysis  let "androidx.camera.core.ImageAnalysis  resolutionInfo "androidx.camera.core.ImageAnalysis  setAnalyzer "androidx.camera.core.ImageAnalysis  build *androidx.camera.core.ImageAnalysis.Builder  setBackpressureStrategy *androidx.camera.core.ImageAnalysis.Builder  setResolutionSelector *androidx.camera.core.ImageAnalysis.Builder  rotationDegrees androidx.camera.core.ImageInfo  close androidx.camera.core.ImageProxy  image androidx.camera.core.ImageProxy  	imageInfo androidx.camera.core.ImageProxy  Builder androidx.camera.core.Preview  SurfaceProvider androidx.camera.core.Preview  also androidx.camera.core.Preview  surfaceProvider androidx.camera.core.Preview  build $androidx.camera.core.Preview.Builder  setResolutionSelector $androidx.camera.core.Preview.Builder  maxZoomRatio androidx.camera.core.ZoomState  minZoomRatio androidx.camera.core.ZoomState  	zoomRatio androidx.camera.core.ZoomState  ResolutionFilter 'androidx.camera.core.resolutionselector  ResolutionSelector 'androidx.camera.core.resolutionselector  ResolutionStrategy 'androidx.camera.core.resolutionselector  <SAM-CONSTRUCTOR> 8androidx.camera.core.resolutionselector.ResolutionFilter  Builder :androidx.camera.core.resolutionselector.ResolutionSelector  build Bandroidx.camera.core.resolutionselector.ResolutionSelector.Builder  setResolutionFilter Bandroidx.camera.core.resolutionselector.ResolutionSelector.Builder  setResolutionStrategy Bandroidx.camera.core.resolutionselector.ResolutionSelector.Builder  'FALLBACK_RULE_CLOSEST_LOWER_THEN_HIGHER :androidx.camera.core.resolutionselector.ResolutionStrategy  ProcessCameraProvider androidx.camera.lifecycle  	Companion /androidx.camera.lifecycle.ProcessCameraProvider  bindToLifecycle /androidx.camera.lifecycle.ProcessCameraProvider  getInstance /androidx.camera.lifecycle.ProcessCameraProvider  	unbindAll /androidx.camera.lifecycle.ProcessCameraProvider  getInstance 9androidx.camera.lifecycle.ProcessCameraProvider.Companion  PreviewView androidx.camera.view  surfaceProvider  androidx.camera.view.PreviewView  ConstraintLayout  androidx.constraintlayout.widget  ActivityMainBinding #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  BroadCastUtils #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  Button #androidx.core.app.ComponentActivity  ExperimentalGetImage #androidx.core.app.ComponentActivity  Float #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  OptIn #androidx.core.app.ComponentActivity  QrScanViewModel #androidx.core.app.ComponentActivity  RequestPermission #androidx.core.app.ComponentActivity  ScanResultsHolder #androidx.core.app.ComponentActivity  SeekBar #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  apply #androidx.core.app.ComponentActivity  clearResults #androidx.core.app.ComponentActivity  format #androidx.core.app.ComponentActivity  getLatestResult #androidx.core.app.ComponentActivity  getValue #androidx.core.app.ComponentActivity  
isNotEmpty #androidx.core.app.ComponentActivity  joinToString #androidx.core.app.ComponentActivity  let #androidx.core.app.ComponentActivity  provideDelegate #androidx.core.app.ComponentActivity  takeLast #androidx.core.app.ComponentActivity  	viewModel #androidx.core.app.ComponentActivity  
viewModels #androidx.core.app.ComponentActivity  OnSeekBarChangeListener +androidx.core.app.ComponentActivity.SeekBar  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  getMainExecutor #androidx.core.content.ContextCompat  scale androidx.core.graphics  ActivityMainBinding &androidx.fragment.app.FragmentActivity  Boolean &androidx.fragment.app.FragmentActivity  BroadCastUtils &androidx.fragment.app.FragmentActivity  ExperimentalGetImage &androidx.fragment.app.FragmentActivity  Int &androidx.fragment.app.FragmentActivity  Intent &androidx.fragment.app.FragmentActivity  RequestPermission &androidx.fragment.app.FragmentActivity  ScanResultsHolder &androidx.fragment.app.FragmentActivity  SeekBar &androidx.fragment.app.FragmentActivity  String &androidx.fragment.app.FragmentActivity  Toast &androidx.fragment.app.FragmentActivity  View &androidx.fragment.app.FragmentActivity  apply &androidx.fragment.app.FragmentActivity  clearResults &androidx.fragment.app.FragmentActivity  format &androidx.fragment.app.FragmentActivity  getLatestResult &androidx.fragment.app.FragmentActivity  getValue &androidx.fragment.app.FragmentActivity  
isNotEmpty &androidx.fragment.app.FragmentActivity  joinToString &androidx.fragment.app.FragmentActivity  let &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  provideDelegate &androidx.fragment.app.FragmentActivity  takeLast &androidx.fragment.app.FragmentActivity  	viewModel &androidx.fragment.app.FragmentActivity  
viewModels &androidx.fragment.app.FragmentActivity  OnSeekBarChangeListener .androidx.fragment.app.FragmentActivity.SeekBar  AndroidViewModel androidx.lifecycle  LifecycleOwner androidx.lifecycle  LiveData androidx.lifecycle  MutableLiveData androidx.lifecycle  Observer androidx.lifecycle  	onCleared #androidx.lifecycle.AndroidViewModel  observe androidx.lifecycle.LiveData  value androidx.lifecycle.LiveData  observe "androidx.lifecycle.MutableLiveData  	postValue "androidx.lifecycle.MutableLiveData  value "androidx.lifecycle.MutableLiveData  <SAM-CONSTRUCTOR> androidx.lifecycle.Observer  Barcode androidx.lifecycle.ViewModel  
CameraManager androidx.lifecycle.ViewModel  ExperimentalGetImage androidx.lifecycle.ViewModel  Float androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  MutableLiveData androidx.lifecycle.ViewModel  Pair androidx.lifecycle.ViewModel  ScanResultsHolder androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  
coerceAtLeast androidx.lifecycle.ViewModel  coerceAtMost androidx.lifecycle.ViewModel  	onCleared androidx.lifecycle.ViewModel  OnCompleteListener com.google.android.gms.tasks  OnFailureListener com.google.android.gms.tasks  OnSuccessListener com.google.android.gms.tasks  Task com.google.android.gms.tasks  <SAM-CONSTRUCTOR> /com.google.android.gms.tasks.OnCompleteListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnFailureListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnSuccessListener  addOnCompleteListener !com.google.android.gms.tasks.Task  addOnFailureListener !com.google.android.gms.tasks.Task  addOnSuccessListener !com.google.android.gms.tasks.Task  ListenableFuture !com.google.common.util.concurrent  addListener 2com.google.common.util.concurrent.ListenableFuture  get 2com.google.common.util.concurrent.ListenableFuture  BarcodeScanner com.google.mlkit.vision.barcode  BarcodeScannerOptions com.google.mlkit.vision.barcode  BarcodeScanning com.google.mlkit.vision.barcode  process .com.google.mlkit.vision.barcode.BarcodeScanner  Builder 5com.google.mlkit.vision.barcode.BarcodeScannerOptions  build =com.google.mlkit.vision.barcode.BarcodeScannerOptions.Builder  setBarcodeFormats =com.google.mlkit.vision.barcode.BarcodeScannerOptions.Builder  	getClient /com.google.mlkit.vision.barcode.BarcodeScanning  Barcode &com.google.mlkit.vision.barcode.common  FORMAT_QR_CODE .com.google.mlkit.vision.barcode.common.Barcode  boundingBox .com.google.mlkit.vision.barcode.common.Barcode  rawValue .com.google.mlkit.vision.barcode.common.Barcode  
InputImage com.google.mlkit.vision.common  fromMediaImage )com.google.mlkit.vision.common.InputImage  VerticalSeekBar )com.h6ah4i.android.widget.verticalseekbar  max 9com.h6ah4i.android.widget.verticalseekbar.VerticalSeekBar  progress 9com.h6ah4i.android.widget.verticalseekbar.VerticalSeekBar  setOnSeekBarChangeListener 9com.h6ah4i.android.widget.verticalseekbar.VerticalSeekBar  QrScanRepository com.yancao.qrscanner  R com.yancao.qrscanner  Barcode com.yancao.qrscanner.camera  BarcodeScannerOptions com.yancao.qrscanner.camera  BarcodeScanning com.yancao.qrscanner.camera  Boolean com.yancao.qrscanner.camera  
CameraControl com.yancao.qrscanner.camera  
CameraInfo com.yancao.qrscanner.camera  
CameraManager com.yancao.qrscanner.camera  CameraSelector com.yancao.qrscanner.camera  Context com.yancao.qrscanner.camera  
ContextCompat com.yancao.qrscanner.camera  	Exception com.yancao.qrscanner.camera  	Executors com.yancao.qrscanner.camera  ExperimentalCamera2Interop com.yancao.qrscanner.camera  ExperimentalGetImage com.yancao.qrscanner.camera  Float com.yancao.qrscanner.camera  
ImageAnalysis com.yancao.qrscanner.camera  
ImageProxy com.yancao.qrscanner.camera  
InputImage com.yancao.qrscanner.camera  Int com.yancao.qrscanner.camera  LifecycleOwner com.yancao.qrscanner.camera  List com.yancao.qrscanner.camera  Log com.yancao.qrscanner.camera  OptIn com.yancao.qrscanner.camera  Preview com.yancao.qrscanner.camera  PreviewView com.yancao.qrscanner.camera  ProcessCameraProvider com.yancao.qrscanner.camera  RealtimeQRAnalyzer com.yancao.qrscanner.camera  ResolutionSelector com.yancao.qrscanner.camera  ResolutionStrategy com.yancao.qrscanner.camera  ScanResultsHolder com.yancao.qrscanner.camera  Size com.yancao.qrscanner.camera  String com.yancao.qrscanner.camera  System com.yancao.qrscanner.camera  Unit com.yancao.qrscanner.camera  UseCase com.yancao.qrscanner.camera  abs com.yancao.qrscanner.camera  addScanResults com.yancao.qrscanner.camera  also com.yancao.qrscanner.camera  coerceIn com.yancao.qrscanner.camera  	emptyList com.yancao.qrscanner.camera  getInstance com.yancao.qrscanner.camera  ifEmpty com.yancao.qrscanner.camera  
isNotEmpty com.yancao.qrscanner.camera  let com.yancao.qrscanner.camera  map com.yancao.qrscanner.camera  
mapNotNull com.yancao.qrscanner.camera  
mutableListOf com.yancao.qrscanner.camera  println com.yancao.qrscanner.camera  sortedBy com.yancao.qrscanner.camera  take com.yancao.qrscanner.camera  toTypedArray com.yancao.qrscanner.camera  CameraSelector )com.yancao.qrscanner.camera.CameraManager  
ContextCompat )com.yancao.qrscanner.camera.CameraManager  	Executors )com.yancao.qrscanner.camera.CameraManager  ExperimentalCamera2Interop )com.yancao.qrscanner.camera.CameraManager  
ImageAnalysis )com.yancao.qrscanner.camera.CameraManager  Log )com.yancao.qrscanner.camera.CameraManager  Preview )com.yancao.qrscanner.camera.CameraManager  ProcessCameraProvider )com.yancao.qrscanner.camera.CameraManager  RealtimeQRAnalyzer )com.yancao.qrscanner.camera.CameraManager  ResolutionSelector )com.yancao.qrscanner.camera.CameraManager  ResolutionStrategy )com.yancao.qrscanner.camera.CameraManager  Size )com.yancao.qrscanner.camera.CameraManager  abs )com.yancao.qrscanner.camera.CameraManager  also )com.yancao.qrscanner.camera.CameraManager  
cameraControl )com.yancao.qrscanner.camera.CameraManager  cameraExecutor )com.yancao.qrscanner.camera.CameraManager  
cameraInfo )com.yancao.qrscanner.camera.CameraManager  cameraProvider )com.yancao.qrscanner.camera.CameraManager  coerceIn )com.yancao.qrscanner.camera.CameraManager  context )com.yancao.qrscanner.camera.CameraManager  getCurrentExposureCompensation )com.yancao.qrscanner.camera.CameraManager  getCurrentZoomRatio )com.yancao.qrscanner.camera.CameraManager  getExposureCompensationStep )com.yancao.qrscanner.camera.CameraManager  getInstance )com.yancao.qrscanner.camera.CameraManager  getMaxExposureCompensation )com.yancao.qrscanner.camera.CameraManager  getMaxZoomRatio )com.yancao.qrscanner.camera.CameraManager  getMinExposureCompensation )com.yancao.qrscanner.camera.CameraManager  getMinZoomRatio )com.yancao.qrscanner.camera.CameraManager  
hasFlashlight )com.yancao.qrscanner.camera.CameraManager  ifEmpty )com.yancao.qrscanner.camera.CameraManager  
imageAnalyzer )com.yancao.qrscanner.camera.CameraManager  let )com.yancao.qrscanner.camera.CameraManager  map )com.yancao.qrscanner.camera.CameraManager  
mutableListOf )com.yancao.qrscanner.camera.CameraManager  
qrAnalyzer )com.yancao.qrscanner.camera.CameraManager  setExposureCompensation )com.yancao.qrscanner.camera.CameraManager  
setFlashlight )com.yancao.qrscanner.camera.CameraManager  setZoomRatio )com.yancao.qrscanner.camera.CameraManager  shutdown )com.yancao.qrscanner.camera.CameraManager  sortedBy )com.yancao.qrscanner.camera.CameraManager  startCamera )com.yancao.qrscanner.camera.CameraManager  take )com.yancao.qrscanner.camera.CameraManager  toTypedArray )com.yancao.qrscanner.camera.CameraManager  Analyzer )com.yancao.qrscanner.camera.ImageAnalysis  Barcode .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  BarcodeScannerOptions .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  BarcodeScanning .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  
InputImage .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  ScanResultsHolder .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  System .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  addScanResults .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  analyzeInterval .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  	emptyList .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  
isNotEmpty .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  lastAnalyzedTimestamp .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  
mapNotNull .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  onQRCodeDetected .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  onQRCodeWithPosition .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  options .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  println .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  scanner .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  ActivityMainBinding  com.yancao.qrscanner.databinding  btnBroadcast 4com.yancao.qrscanner.databinding.ActivityMainBinding  
btnFlashlight 4com.yancao.qrscanner.databinding.ActivityMainBinding  btnScan 4com.yancao.qrscanner.databinding.ActivityMainBinding  exposureControlPanel 4com.yancao.qrscanner.databinding.ActivityMainBinding  inflate 4com.yancao.qrscanner.databinding.ActivityMainBinding  	mySeekBar 4com.yancao.qrscanner.databinding.ActivityMainBinding  previewView 4com.yancao.qrscanner.databinding.ActivityMainBinding  
qrOverlayView 4com.yancao.qrscanner.databinding.ActivityMainBinding  root 4com.yancao.qrscanner.databinding.ActivityMainBinding  seekBarZoom 4com.yancao.qrscanner.databinding.ActivityMainBinding  tvExposureValue 4com.yancao.qrscanner.databinding.ActivityMainBinding  
tvScanResults 4com.yancao.qrscanner.databinding.ActivityMainBinding  tvZoomRatio 4com.yancao.qrscanner.databinding.ActivityMainBinding  zoomSliderContainer 4com.yancao.qrscanner.databinding.ActivityMainBinding  Boolean com.yancao.qrscanner.domain  Int com.yancao.qrscanner.domain  List com.yancao.qrscanner.domain  MutableList com.yancao.qrscanner.domain  ScanResultsHolder com.yancao.qrscanner.domain  String com.yancao.qrscanner.domain  Synchronized com.yancao.qrscanner.domain  Volatile com.yancao.qrscanner.domain  forEach com.yancao.qrscanner.domain  
isNotBlank com.yancao.qrscanner.domain  
isNotEmpty com.yancao.qrscanner.domain  
lastOrNull com.yancao.qrscanner.domain  
mutableListOf com.yancao.qrscanner.domain  println com.yancao.qrscanner.domain  toList com.yancao.qrscanner.domain  _scanResults -com.yancao.qrscanner.domain.ScanResultsHolder  addScanResults -com.yancao.qrscanner.domain.ScanResultsHolder  clearResults -com.yancao.qrscanner.domain.ScanResultsHolder  getLatestResult -com.yancao.qrscanner.domain.ScanResultsHolder  
isNotBlank -com.yancao.qrscanner.domain.ScanResultsHolder  
isNotEmpty -com.yancao.qrscanner.domain.ScanResultsHolder  
lastOrNull -com.yancao.qrscanner.domain.ScanResultsHolder  
mutableListOf -com.yancao.qrscanner.domain.ScanResultsHolder  println -com.yancao.qrscanner.domain.ScanResultsHolder  scanResults -com.yancao.qrscanner.domain.ScanResultsHolder  toList -com.yancao.qrscanner.domain.ScanResultsHolder  ActivityMainBinding com.yancao.qrscanner.ui  AppCompatActivity com.yancao.qrscanner.ui  AttributeSet com.yancao.qrscanner.ui  Barcode com.yancao.qrscanner.ui  Boolean com.yancao.qrscanner.ui  BroadCastUtils com.yancao.qrscanner.ui  Bundle com.yancao.qrscanner.ui  Button com.yancao.qrscanner.ui  Canvas com.yancao.qrscanner.ui  Color com.yancao.qrscanner.ui  Context com.yancao.qrscanner.ui  DetectedQRInfo com.yancao.qrscanner.ui  ExperimentalGetImage com.yancao.qrscanner.ui  Float com.yancao.qrscanner.ui  Int com.yancao.qrscanner.ui  Intent com.yancao.qrscanner.ui  JvmOverloads com.yancao.qrscanner.ui  List com.yancao.qrscanner.ui  Log com.yancao.qrscanner.ui  OptIn com.yancao.qrscanner.ui  Paint com.yancao.qrscanner.ui  QrCodeOverlayView com.yancao.qrscanner.ui  QrScanActivity com.yancao.qrscanner.ui  QrScanViewModel com.yancao.qrscanner.ui  Rect com.yancao.qrscanner.ui  RectF com.yancao.qrscanner.ui  RequestPermission com.yancao.qrscanner.ui  ScanResultsHolder com.yancao.qrscanner.ui  SeekBar com.yancao.qrscanner.ui  String com.yancao.qrscanner.ui  Toast com.yancao.qrscanner.ui  Typeface com.yancao.qrscanner.ui  View com.yancao.qrscanner.ui  apply com.yancao.qrscanner.ui  clearResults com.yancao.qrscanner.ui  forEach com.yancao.qrscanner.ui  format com.yancao.qrscanner.ui  getLatestResult com.yancao.qrscanner.ui  getValue com.yancao.qrscanner.ui  
isNotEmpty com.yancao.qrscanner.ui  joinToString com.yancao.qrscanner.ui  let com.yancao.qrscanner.ui  minOf com.yancao.qrscanner.ui  
mutableListOf com.yancao.qrscanner.ui  provideDelegate com.yancao.qrscanner.ui  takeLast com.yancao.qrscanner.ui  	viewModel com.yancao.qrscanner.ui  AttributeSet )com.yancao.qrscanner.ui.QrCodeOverlayView  Barcode )com.yancao.qrscanner.ui.QrCodeOverlayView  Canvas )com.yancao.qrscanner.ui.QrCodeOverlayView  Color )com.yancao.qrscanner.ui.QrCodeOverlayView  Context )com.yancao.qrscanner.ui.QrCodeOverlayView  DetectedQRInfo )com.yancao.qrscanner.ui.QrCodeOverlayView  Float )com.yancao.qrscanner.ui.QrCodeOverlayView  Int )com.yancao.qrscanner.ui.QrCodeOverlayView  JvmOverloads )com.yancao.qrscanner.ui.QrCodeOverlayView  List )com.yancao.qrscanner.ui.QrCodeOverlayView  Log )com.yancao.qrscanner.ui.QrCodeOverlayView  Paint )com.yancao.qrscanner.ui.QrCodeOverlayView  Rect )com.yancao.qrscanner.ui.QrCodeOverlayView  RectF )com.yancao.qrscanner.ui.QrCodeOverlayView  String )com.yancao.qrscanner.ui.QrCodeOverlayView  Typeface )com.yancao.qrscanner.ui.QrCodeOverlayView  apply )com.yancao.qrscanner.ui.QrCodeOverlayView  clearDetectedQRCodes )com.yancao.qrscanner.ui.QrCodeOverlayView  detectedQRCodes )com.yancao.qrscanner.ui.QrCodeOverlayView  
drawFrequency )com.yancao.qrscanner.ui.QrCodeOverlayView  drawQRCodeFromBarcode )com.yancao.qrscanner.ui.QrCodeOverlayView  drawQRFrame )com.yancao.qrscanner.ui.QrCodeOverlayView  drawTextNearPoint )com.yancao.qrscanner.ui.QrCodeOverlayView  frameCounter )com.yancao.qrscanner.ui.QrCodeOverlayView  height )com.yancao.qrscanner.ui.QrCodeOverlayView  
invalidate )com.yancao.qrscanner.ui.QrCodeOverlayView  let )com.yancao.qrscanner.ui.QrCodeOverlayView  minOf )com.yancao.qrscanner.ui.QrCodeOverlayView  
mutableListOf )com.yancao.qrscanner.ui.QrCodeOverlayView  scannedQRPaint )com.yancao.qrscanner.ui.QrCodeOverlayView  setDrawFrequency )com.yancao.qrscanner.ui.QrCodeOverlayView  	textPaint )com.yancao.qrscanner.ui.QrCodeOverlayView  updateDetectedQRCodes )com.yancao.qrscanner.ui.QrCodeOverlayView  width )com.yancao.qrscanner.ui.QrCodeOverlayView  barcode 8com.yancao.qrscanner.ui.QrCodeOverlayView.DetectedQRInfo  offsetX 8com.yancao.qrscanner.ui.QrCodeOverlayView.DetectedQRInfo  offsetY 8com.yancao.qrscanner.ui.QrCodeOverlayView.DetectedQRInfo  scaleFactor 8com.yancao.qrscanner.ui.QrCodeOverlayView.DetectedQRInfo  ActivityMainBinding &com.yancao.qrscanner.ui.QrScanActivity  BroadCastUtils &com.yancao.qrscanner.ui.QrScanActivity  ExperimentalGetImage &com.yancao.qrscanner.ui.QrScanActivity  Intent &com.yancao.qrscanner.ui.QrScanActivity  RequestPermission &com.yancao.qrscanner.ui.QrScanActivity  ScanResultsHolder &com.yancao.qrscanner.ui.QrScanActivity  String &com.yancao.qrscanner.ui.QrScanActivity  Toast &com.yancao.qrscanner.ui.QrScanActivity  View &com.yancao.qrscanner.ui.QrScanActivity  apply &com.yancao.qrscanner.ui.QrScanActivity  binding &com.yancao.qrscanner.ui.QrScanActivity  clearResults &com.yancao.qrscanner.ui.QrScanActivity  format &com.yancao.qrscanner.ui.QrScanActivity  getLatestResult &com.yancao.qrscanner.ui.QrScanActivity  getValue &com.yancao.qrscanner.ui.QrScanActivity  handleBroadcastButtonClick &com.yancao.qrscanner.ui.QrScanActivity  handleFlashlightButtonClick &com.yancao.qrscanner.ui.QrScanActivity  handleScanButtonClick &com.yancao.qrscanner.ui.QrScanActivity  
isNotEmpty &com.yancao.qrscanner.ui.QrScanActivity  joinToString &com.yancao.qrscanner.ui.QrScanActivity  layoutInflater &com.yancao.qrscanner.ui.QrScanActivity  let &com.yancao.qrscanner.ui.QrScanActivity  permissionHelper &com.yancao.qrscanner.ui.QrScanActivity  provideDelegate &com.yancao.qrscanner.ui.QrScanActivity  
sendBroadcast &com.yancao.qrscanner.ui.QrScanActivity  setContentView &com.yancao.qrscanner.ui.QrScanActivity  setupClickListeners &com.yancao.qrscanner.ui.QrScanActivity  setupExposureControl &com.yancao.qrscanner.ui.QrScanActivity  setupObservers &com.yancao.qrscanner.ui.QrScanActivity  setupZoomControl &com.yancao.qrscanner.ui.QrScanActivity  takeLast &com.yancao.qrscanner.ui.QrScanActivity  updateControlsVisibility &com.yancao.qrscanner.ui.QrScanActivity  updateFlashlightButtonText &com.yancao.qrscanner.ui.QrScanActivity  updateScanButtonText &com.yancao.qrscanner.ui.QrScanActivity  updateScanResultsDisplay &com.yancao.qrscanner.ui.QrScanActivity  updateZoomDisplay &com.yancao.qrscanner.ui.QrScanActivity  	viewModel &com.yancao.qrscanner.ui.QrScanActivity  
viewModels &com.yancao.qrscanner.ui.QrScanActivity  OnSeekBarChangeListener com.yancao.qrscanner.ui.SeekBar  Activity com.yancao.qrscanner.utils  ActivityResultContracts com.yancao.qrscanner.utils  Bitmap com.yancao.qrscanner.utils  BitmapSizeReduce com.yancao.qrscanner.utils  BroadCastUtils com.yancao.qrscanner.utils  Build com.yancao.qrscanner.utils  ComponentActivity com.yancao.qrscanner.utils  
ContextCompat com.yancao.qrscanner.utils  DisplayMetrics com.yancao.qrscanner.utils  DisplayUtils com.yancao.qrscanner.utils  ImageHolder com.yancao.qrscanner.utils  Int com.yancao.qrscanner.utils  Manifest com.yancao.qrscanner.utils  PackageManager com.yancao.qrscanner.utils  Point com.yancao.qrscanner.utils  RequestPermission com.yancao.qrscanner.utils  Suppress com.yancao.qrscanner.utils  Unit com.yancao.qrscanner.utils  min com.yancao.qrscanner.utils  scale com.yancao.qrscanner.utils  min +com.yancao.qrscanner.utils.BitmapSizeReduce  scale +com.yancao.qrscanner.utils.BitmapSizeReduce  BROADCAST_ACTION )com.yancao.qrscanner.utils.BroadCastUtils  BROADCAST_RAW_DATA )com.yancao.qrscanner.utils.BroadCastUtils  Activity 'com.yancao.qrscanner.utils.DisplayUtils  Build 'com.yancao.qrscanner.utils.DisplayUtils  DisplayMetrics 'com.yancao.qrscanner.utils.DisplayUtils  Point 'com.yancao.qrscanner.utils.DisplayUtils  Suppress 'com.yancao.qrscanner.utils.DisplayUtils  Build 1com.yancao.qrscanner.utils.DisplayUtils.Companion  DisplayMetrics 1com.yancao.qrscanner.utils.DisplayUtils.Companion  Point 1com.yancao.qrscanner.utils.DisplayUtils.Companion  ActivityResultContracts ,com.yancao.qrscanner.utils.RequestPermission  
ContextCompat ,com.yancao.qrscanner.utils.RequestPermission  Manifest ,com.yancao.qrscanner.utils.RequestPermission  PackageManager ,com.yancao.qrscanner.utils.RequestPermission  activity ,com.yancao.qrscanner.utils.RequestPermission  checkAndRequestCameraPermission ,com.yancao.qrscanner.utils.RequestPermission  onPermissionDenied ,com.yancao.qrscanner.utils.RequestPermission  onPermissionGranted ,com.yancao.qrscanner.utils.RequestPermission  permissionLauncher ,com.yancao.qrscanner.utils.RequestPermission  AndroidViewModel com.yancao.qrscanner.viewModel  Application com.yancao.qrscanner.viewModel  Barcode com.yancao.qrscanner.viewModel  Boolean com.yancao.qrscanner.viewModel  
CameraManager com.yancao.qrscanner.viewModel  ExperimentalGetImage com.yancao.qrscanner.viewModel  Float com.yancao.qrscanner.viewModel  Int com.yancao.qrscanner.viewModel  LifecycleOwner com.yancao.qrscanner.viewModel  List com.yancao.qrscanner.viewModel  LiveData com.yancao.qrscanner.viewModel  MutableLiveData com.yancao.qrscanner.viewModel  OptIn com.yancao.qrscanner.viewModel  Pair com.yancao.qrscanner.viewModel  PreviewView com.yancao.qrscanner.viewModel  QrScanViewModel com.yancao.qrscanner.viewModel  ScanResultsHolder com.yancao.qrscanner.viewModel  String com.yancao.qrscanner.viewModel  
coerceAtLeast com.yancao.qrscanner.viewModel  coerceAtMost com.yancao.qrscanner.viewModel  
CameraManager .com.yancao.qrscanner.viewModel.QrScanViewModel  ExperimentalGetImage .com.yancao.qrscanner.viewModel.QrScanViewModel  MutableLiveData .com.yancao.qrscanner.viewModel.QrScanViewModel  Pair .com.yancao.qrscanner.viewModel.QrScanViewModel  ScanResultsHolder .com.yancao.qrscanner.viewModel.QrScanViewModel  _exposureCompensation .com.yancao.qrscanner.viewModel.QrScanViewModel  _exposureCompensationStep .com.yancao.qrscanner.viewModel.QrScanViewModel  _maxExposureCompensation .com.yancao.qrscanner.viewModel.QrScanViewModel  _minExposureCompensation .com.yancao.qrscanner.viewModel.QrScanViewModel  
cameraManager .com.yancao.qrscanner.viewModel.QrScanViewModel  
coerceAtLeast .com.yancao.qrscanner.viewModel.QrScanViewModel  coerceAtMost .com.yancao.qrscanner.viewModel.QrScanViewModel  exposureCompensation .com.yancao.qrscanner.viewModel.QrScanViewModel  getAllScanResults .com.yancao.qrscanner.viewModel.QrScanViewModel  
hasFlashlight .com.yancao.qrscanner.viewModel.QrScanViewModel  initializeCameraControlStates .com.yancao.qrscanner.viewModel.QrScanViewModel  isFlashlightEnabled .com.yancao.qrscanner.viewModel.QrScanViewModel  isScanningEnabled .com.yancao.qrscanner.viewModel.QrScanViewModel  maxExposureCompensation .com.yancao.qrscanner.viewModel.QrScanViewModel  maxZoomRatio .com.yancao.qrscanner.viewModel.QrScanViewModel  minExposureCompensation .com.yancao.qrscanner.viewModel.QrScanViewModel  minZoomRatio .com.yancao.qrscanner.viewModel.QrScanViewModel  qrCodePositions .com.yancao.qrscanner.viewModel.QrScanViewModel  realtimeScanResults .com.yancao.qrscanner.viewModel.QrScanViewModel  setExposureCompensation .com.yancao.qrscanner.viewModel.QrScanViewModel  setZoomRatio .com.yancao.qrscanner.viewModel.QrScanViewModel  startCamera .com.yancao.qrscanner.viewModel.QrScanViewModel  startRealtimeScanning .com.yancao.qrscanner.viewModel.QrScanViewModel  stopRealtimeScanning .com.yancao.qrscanner.viewModel.QrScanViewModel  toggleFlashlight .com.yancao.qrscanner.viewModel.QrScanViewModel  	zoomRatio .com.yancao.qrscanner.viewModel.QrScanViewModel  	Exception 	java.lang  Runnable 	java.lang  message java.lang.Exception  printStackTrace java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  currentTimeMillis java.lang.System  Executor java.util.concurrent  	Executors java.util.concurrent  shutdown $java.util.concurrent.ExecutorService  newSingleThreadExecutor java.util.concurrent.Executors  Array kotlin  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	Function3 kotlin  Lazy kotlin  Pair kotlin  Result kotlin  String kotlin  Suppress kotlin  also kotlin  apply kotlin  getValue kotlin  let kotlin  map kotlin  toList kotlin  not kotlin.Boolean  coerceIn kotlin.Float  	compareTo kotlin.Float  div kotlin.Float  minus kotlin.Float  plus kotlin.Float  times kotlin.Float  toInt kotlin.Float  invoke kotlin.Function0  invoke kotlin.Function1  invoke kotlin.Function3  
coerceAtLeast 
kotlin.Int  coerceAtMost 
kotlin.Int  coerceIn 
kotlin.Int  	compareTo 
kotlin.Int  div 
kotlin.Int  inc 
kotlin.Int  let 
kotlin.Int  minus 
kotlin.Int  plus 
kotlin.Int  rem 
kotlin.Int  times 
kotlin.Int  toFloat 
kotlin.Int  toString 
kotlin.Int  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  	compareTo kotlin.Long  minus kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  	Companion 
kotlin.String  format 
kotlin.String  
isNotBlank 
kotlin.String  length 
kotlin.String  plus 
kotlin.String  format kotlin.String.Companion  message kotlin.Throwable  printStackTrace kotlin.Throwable  List kotlin.collections  MutableList kotlin.collections  	emptyList kotlin.collections  forEach kotlin.collections  getValue kotlin.collections  ifEmpty kotlin.collections  
isNotEmpty kotlin.collections  joinToString kotlin.collections  
lastOrNull kotlin.collections  map kotlin.collections  
mapNotNull kotlin.collections  min kotlin.collections  minOf kotlin.collections  
mutableListOf kotlin.collections  sortedBy kotlin.collections  take kotlin.collections  takeLast kotlin.collections  toList kotlin.collections  toTypedArray kotlin.collections  ifEmpty kotlin.collections.List  isEmpty kotlin.collections.List  joinToString kotlin.collections.List  map kotlin.collections.List  size kotlin.collections.List  take kotlin.collections.List  takeLast kotlin.collections.List  add kotlin.collections.MutableList  clear kotlin.collections.MutableList  contains kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  
lastOrNull kotlin.collections.MutableList  size kotlin.collections.MutableList  toList kotlin.collections.MutableList  toTypedArray kotlin.collections.MutableList  minOf kotlin.comparisons  println 	kotlin.io  JvmOverloads 
kotlin.jvm  Synchronized 
kotlin.jvm  Volatile 
kotlin.jvm  abs kotlin.math  min kotlin.math  
coerceAtLeast 
kotlin.ranges  coerceAtMost 
kotlin.ranges  coerceIn 
kotlin.ranges  
lastOrNull 
kotlin.ranges  
KProperty1 kotlin.reflect  Sequence kotlin.sequences  forEach kotlin.sequences  ifEmpty kotlin.sequences  joinToString kotlin.sequences  
lastOrNull kotlin.sequences  map kotlin.sequences  
mapNotNull kotlin.sequences  min kotlin.sequences  minOf kotlin.sequences  sortedBy kotlin.sequences  take kotlin.sequences  toList kotlin.sequences  forEach kotlin.text  format kotlin.text  ifEmpty kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  
lastOrNull kotlin.text  map kotlin.text  
mapNotNull kotlin.text  min kotlin.text  minOf kotlin.text  take kotlin.text  takeLast kotlin.text  toList kotlin.text  simple_spinner_dropdown_item android.R.layout  simple_spinner_item android.R.layout  ControlPanelManager android.app.Activity  
ScanConfig android.app.Activity  ControlPanelManager android.content.Context  
ScanConfig android.content.Context  ControlPanelManager android.content.ContextWrapper  
ScanConfig android.content.ContextWrapper  Editable android.text  TextWatcher android.text  ControlPanelManager  android.view.ContextThemeWrapper  
ScanConfig  android.view.ContextThemeWrapper  from android.view.LayoutInflater  inflate android.view.LayoutInflater  findViewById android.view.View  AdapterView android.widget  ArrayAdapter android.widget  Context android.widget  EditText android.widget  	Exception android.widget  FrameLayout android.widget  
FrequencyUnit android.widget  Int android.widget  LayoutInflater android.widget  Long android.widget  R android.widget  
ScanConfig android.widget  Spinner android.widget  Unit android.widget  View android.widget  addTextChangedListener android.widget  android android.widget  map android.widget  toIntOrNull android.widget  updateFrameDurationVisibility android.widget  adapter android.widget.AbsSpinner  setSelection android.widget.AbsSpinner  OnItemSelectedListener android.widget.AdapterView  onItemSelectedListener android.widget.AdapterView  selectedItemPosition android.widget.AdapterView  setDropDownViewResource android.widget.ArrayAdapter  addTextChangedListener android.widget.EditText  setText android.widget.EditText  text android.widget.EditText  setOnClickListener android.widget.FrameLayout  
visibility android.widget.FrameLayout  adapter android.widget.Spinner  onItemSelectedListener android.widget.Spinner  selectedItemPosition android.widget.Spinner  setSelection android.widget.Spinner  addTextChangedListener android.widget.TextView  setText android.widget.TextView  ControlPanelManager #androidx.activity.ComponentActivity  
ScanConfig #androidx.activity.ComponentActivity  ControlPanelManager -androidx.activity.ComponentActivity.Companion  
ScanConfig -androidx.activity.ComponentActivity.Companion  ControlPanelManager (androidx.appcompat.app.AppCompatActivity  
ScanConfig (androidx.appcompat.app.AppCompatActivity  ControlPanelManager #androidx.core.app.ComponentActivity  
ScanConfig #androidx.core.app.ComponentActivity  addTextChangedListener androidx.core.widget  ControlPanelManager &androidx.fragment.app.FragmentActivity  
ScanConfig &androidx.fragment.app.FragmentActivity  
ScanConfig androidx.lifecycle.ViewModel  btn_apply_settings com.yancao.qrscanner.R.id  btn_cancel_settings com.yancao.qrscanner.R.id  et_draw_frequency com.yancao.qrscanner.R.id  et_scan_frequency com.yancao.qrscanner.R.id  spinner_draw_unit com.yancao.qrscanner.R.id  spinner_scan_unit com.yancao.qrscanner.R.id  control_panel_overlay com.yancao.qrscanner.R.layout  Long com.yancao.qrscanner.camera  btnSettings 4com.yancao.qrscanner.databinding.ActivityMainBinding  settingsOverlay 4com.yancao.qrscanner.databinding.ActivityMainBinding  
FrequencyUnit com.yancao.qrscanner.domain  Long com.yancao.qrscanner.domain  
ScanConfig com.yancao.qrscanner.domain  maxOf com.yancao.qrscanner.domain  FRAMES )com.yancao.qrscanner.domain.FrequencyUnit  MILLISECONDS )com.yancao.qrscanner.domain.FrequencyUnit  displayName )com.yancao.qrscanner.domain.FrequencyUnit  ordinal )com.yancao.qrscanner.domain.FrequencyUnit  values )com.yancao.qrscanner.domain.FrequencyUnit  
FrequencyUnit &com.yancao.qrscanner.domain.ScanConfig  drawFrequencyUnit &com.yancao.qrscanner.domain.ScanConfig  drawFrequencyValue &com.yancao.qrscanner.domain.ScanConfig  getDrawFrequencyFrames &com.yancao.qrscanner.domain.ScanConfig  maxOf &com.yancao.qrscanner.domain.ScanConfig  scanFrequencyUnit &com.yancao.qrscanner.domain.ScanConfig  scanFrequencyValue &com.yancao.qrscanner.domain.ScanConfig  ControlPanelManager com.yancao.qrscanner.ui  
ScanConfig com.yancao.qrscanner.ui  ControlPanelManager &com.yancao.qrscanner.ui.QrScanActivity  
ScanConfig &com.yancao.qrscanner.ui.QrScanActivity  applyScanConfig &com.yancao.qrscanner.ui.QrScanActivity  controlPanelManager &com.yancao.qrscanner.ui.QrScanActivity  currentScanConfig &com.yancao.qrscanner.ui.QrScanActivity  AdapterView com.yancao.qrscanner.utils  ArrayAdapter com.yancao.qrscanner.utils  Button com.yancao.qrscanner.utils  Context com.yancao.qrscanner.utils  ControlPanelManager com.yancao.qrscanner.utils  EditText com.yancao.qrscanner.utils  	Exception com.yancao.qrscanner.utils  FrameLayout com.yancao.qrscanner.utils  
FrequencyUnit com.yancao.qrscanner.utils  LayoutInflater com.yancao.qrscanner.utils  LinearLayout com.yancao.qrscanner.utils  Long com.yancao.qrscanner.utils  R com.yancao.qrscanner.utils  
ScanConfig com.yancao.qrscanner.utils  Spinner com.yancao.qrscanner.utils  Toast com.yancao.qrscanner.utils  View com.yancao.qrscanner.utils  addTextChangedListener com.yancao.qrscanner.utils  android com.yancao.qrscanner.utils  map com.yancao.qrscanner.utils  toIntOrNull com.yancao.qrscanner.utils  updateFrameDurationVisibility com.yancao.qrscanner.utils  OnItemSelectedListener &com.yancao.qrscanner.utils.AdapterView  ArrayAdapter .com.yancao.qrscanner.utils.ControlPanelManager  
FrequencyUnit .com.yancao.qrscanner.utils.ControlPanelManager  LayoutInflater .com.yancao.qrscanner.utils.ControlPanelManager  R .com.yancao.qrscanner.utils.ControlPanelManager  
ScanConfig .com.yancao.qrscanner.utils.ControlPanelManager  Toast .com.yancao.qrscanner.utils.ControlPanelManager  View .com.yancao.qrscanner.utils.ControlPanelManager  addTextChangedListener .com.yancao.qrscanner.utils.ControlPanelManager  android .com.yancao.qrscanner.utils.ControlPanelManager  
applySettings .com.yancao.qrscanner.utils.ControlPanelManager  context .com.yancao.qrscanner.utils.ControlPanelManager  controlPanelView .com.yancao.qrscanner.utils.ControlPanelManager  
currentConfig .com.yancao.qrscanner.utils.ControlPanelManager  etDrawFrequency .com.yancao.qrscanner.utils.ControlPanelManager  etScanFrequency .com.yancao.qrscanner.utils.ControlPanelManager  hideControlPanel .com.yancao.qrscanner.utils.ControlPanelManager  initializeControlPanel .com.yancao.qrscanner.utils.ControlPanelManager  layoutFrameDuration .com.yancao.qrscanner.utils.ControlPanelManager  map .com.yancao.qrscanner.utils.ControlPanelManager  onConfigChanged .com.yancao.qrscanner.utils.ControlPanelManager  overlayContainer .com.yancao.qrscanner.utils.ControlPanelManager  setupListeners .com.yancao.qrscanner.utils.ControlPanelManager  
setupSpinners .com.yancao.qrscanner.utils.ControlPanelManager  spinnerDrawUnit .com.yancao.qrscanner.utils.ControlPanelManager  spinnerScanUnit .com.yancao.qrscanner.utils.ControlPanelManager  toIntOrNull .com.yancao.qrscanner.utils.ControlPanelManager  updateFrameDurationVisibility .com.yancao.qrscanner.utils.ControlPanelManager  updateUIFromConfig .com.yancao.qrscanner.utils.ControlPanelManager  
ScanConfig com.yancao.qrscanner.viewModel  
ScanConfig .com.yancao.qrscanner.viewModel.QrScanViewModel  
scanConfig .com.yancao.qrscanner.viewModel.QrScanViewModel  updateScanConfig .com.yancao.qrscanner.viewModel.QrScanViewModel  toString 
kotlin.Any  get kotlin.Array  map kotlin.Array  String kotlin.Enum  toLong 
kotlin.Int  toIntOrNull 
kotlin.String  maxOf kotlin.collections  maxOf kotlin.comparisons  maxOf kotlin.sequences  maxOf kotlin.text  toIntOrNull kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         