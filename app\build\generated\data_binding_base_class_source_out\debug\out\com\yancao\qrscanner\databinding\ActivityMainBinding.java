// Generated by view binder compiler. Do not edit!
package com.yancao.qrscanner.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.camera.view.PreviewView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.h6ah4i.android.widget.verticalseekbar.VerticalSeekBar;
import com.yancao.qrscanner.R;
import com.yancao.qrscanner.ui.QrCodeOverlayView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button btnBroadcast;

  @NonNull
  public final Button btnFlashlight;

  @NonNull
  public final Button btnScan;

  @NonNull
  public final LinearLayout buttonContainer;

  @NonNull
  public final LinearLayout controlPanel;

  @NonNull
  public final LinearLayout exposureControlPanel;

  @NonNull
  public final VerticalSeekBar mySeekBar;

  @NonNull
  public final PreviewView previewView;

  @NonNull
  public final QrCodeOverlayView qrOverlayView;

  @NonNull
  public final SeekBar seekBarZoom;

  @NonNull
  public final TextView tvExposureValue;

  @NonNull
  public final TextView tvScanResults;

  @NonNull
  public final TextView tvZoomRatio;

  @NonNull
  public final FrameLayout zoomControlPanel;

  @NonNull
  public final LinearLayout zoomSliderContainer;

  private ActivityMainBinding(@NonNull ConstraintLayout rootView, @NonNull Button btnBroadcast,
      @NonNull Button btnFlashlight, @NonNull Button btnScan, @NonNull LinearLayout buttonContainer,
      @NonNull LinearLayout controlPanel, @NonNull LinearLayout exposureControlPanel,
      @NonNull VerticalSeekBar mySeekBar, @NonNull PreviewView previewView,
      @NonNull QrCodeOverlayView qrOverlayView, @NonNull SeekBar seekBarZoom,
      @NonNull TextView tvExposureValue, @NonNull TextView tvScanResults,
      @NonNull TextView tvZoomRatio, @NonNull FrameLayout zoomControlPanel,
      @NonNull LinearLayout zoomSliderContainer) {
    this.rootView = rootView;
    this.btnBroadcast = btnBroadcast;
    this.btnFlashlight = btnFlashlight;
    this.btnScan = btnScan;
    this.buttonContainer = buttonContainer;
    this.controlPanel = controlPanel;
    this.exposureControlPanel = exposureControlPanel;
    this.mySeekBar = mySeekBar;
    this.previewView = previewView;
    this.qrOverlayView = qrOverlayView;
    this.seekBarZoom = seekBarZoom;
    this.tvExposureValue = tvExposureValue;
    this.tvScanResults = tvScanResults;
    this.tvZoomRatio = tvZoomRatio;
    this.zoomControlPanel = zoomControlPanel;
    this.zoomSliderContainer = zoomSliderContainer;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_broadcast;
      Button btnBroadcast = ViewBindings.findChildViewById(rootView, id);
      if (btnBroadcast == null) {
        break missingId;
      }

      id = R.id.btn_flashlight;
      Button btnFlashlight = ViewBindings.findChildViewById(rootView, id);
      if (btnFlashlight == null) {
        break missingId;
      }

      id = R.id.btn_scan;
      Button btnScan = ViewBindings.findChildViewById(rootView, id);
      if (btnScan == null) {
        break missingId;
      }

      id = R.id.button_container;
      LinearLayout buttonContainer = ViewBindings.findChildViewById(rootView, id);
      if (buttonContainer == null) {
        break missingId;
      }

      id = R.id.control_panel;
      LinearLayout controlPanel = ViewBindings.findChildViewById(rootView, id);
      if (controlPanel == null) {
        break missingId;
      }

      id = R.id.exposure_control_panel;
      LinearLayout exposureControlPanel = ViewBindings.findChildViewById(rootView, id);
      if (exposureControlPanel == null) {
        break missingId;
      }

      id = R.id.mySeekBar;
      VerticalSeekBar mySeekBar = ViewBindings.findChildViewById(rootView, id);
      if (mySeekBar == null) {
        break missingId;
      }

      id = R.id.preview_view;
      PreviewView previewView = ViewBindings.findChildViewById(rootView, id);
      if (previewView == null) {
        break missingId;
      }

      id = R.id.qr_overlay_view;
      QrCodeOverlayView qrOverlayView = ViewBindings.findChildViewById(rootView, id);
      if (qrOverlayView == null) {
        break missingId;
      }

      id = R.id.seek_bar_zoom;
      SeekBar seekBarZoom = ViewBindings.findChildViewById(rootView, id);
      if (seekBarZoom == null) {
        break missingId;
      }

      id = R.id.tv_exposure_value;
      TextView tvExposureValue = ViewBindings.findChildViewById(rootView, id);
      if (tvExposureValue == null) {
        break missingId;
      }

      id = R.id.tv_scan_results;
      TextView tvScanResults = ViewBindings.findChildViewById(rootView, id);
      if (tvScanResults == null) {
        break missingId;
      }

      id = R.id.tv_zoom_ratio;
      TextView tvZoomRatio = ViewBindings.findChildViewById(rootView, id);
      if (tvZoomRatio == null) {
        break missingId;
      }

      id = R.id.zoom_control_panel;
      FrameLayout zoomControlPanel = ViewBindings.findChildViewById(rootView, id);
      if (zoomControlPanel == null) {
        break missingId;
      }

      id = R.id.zoom_slider_container;
      LinearLayout zoomSliderContainer = ViewBindings.findChildViewById(rootView, id);
      if (zoomSliderContainer == null) {
        break missingId;
      }

      return new ActivityMainBinding((ConstraintLayout) rootView, btnBroadcast, btnFlashlight,
          btnScan, buttonContainer, controlPanel, exposureControlPanel, mySeekBar, previewView,
          qrOverlayView, seekBarZoom, tvExposureValue, tvScanResults, tvZoomRatio, zoomControlPanel,
          zoomSliderContainer);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
